"""
Trading Algorithm Configuration System
Handles all dynamic settings and parameters for the trading algorithm
"""

import os
from typing import List
from dataclasses import dataclass
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

@dataclass
class TradingConfig:
    """Configuration class for trading algorithm settings"""

    # Risk Management Settings
    per_trade_loss_limit: float = None  # Maximum loss per trade in INR
    max_trades_per_day: int = None  # Maximum number of trades per day

    # Watchlist Settings
    buy_watchlist: List[str] = None  # List of stock symbols for buy tracking
    sell_watchlist: List[str] = None  # List of stock symbols for sell tracking

    # Algorithm Runtime Settings
    timeframe: str = None  # Candle timeframe (1minute, 3minute, 5minute, etc.)
    trading_start_time: str = None  # Market start time
    trading_end_time: str = None  # Market end time

    # Zerodha API Settings
    api_key: str = None
    api_secret: str = None
    access_token: str = None

    # Buy Logic Settings
    skip_first_candles: int = None  # Number of first candles to skip
    direction_filter: str = None  # Trading direction filter

    # Order Settings
    order_type: str = None  # Order type (LIMIT, MARKET)
    product_type: str = None  # Product type (MIS, CNC, NRML)

    # Trading Mode
    trading_mode: str = None  # TESTING or LIVE
    
    def __post_init__(self):
        """Initialize values from environment variables or defaults"""
        # Load from environment variables if not set
        if self.per_trade_loss_limit is None:
            self.per_trade_loss_limit = float(os.getenv('PER_TRADE_LOSS_LIMIT', 1000.0))

        if self.max_trades_per_day is None:
            self.max_trades_per_day = int(os.getenv('MAX_TRADES_PER_DAY', 5))

        if self.timeframe is None:
            self.timeframe = os.getenv('TIMEFRAME', '1minute')

        if self.trading_start_time is None:
            self.trading_start_time = os.getenv('TRADING_START_TIME', '09:15')

        if self.trading_end_time is None:
            self.trading_end_time = os.getenv('TRADING_END_TIME', '15:30')

        if self.api_key is None:
            self.api_key = os.getenv('ZERODHA_API_KEY', '')

        if self.api_secret is None:
            self.api_secret = os.getenv('ZERODHA_API_SECRET', '')

        if self.access_token is None:
            self.access_token = os.getenv('ZERODHA_ACCESS_TOKEN', '')

        if self.skip_first_candles is None:
            self.skip_first_candles = int(os.getenv('SKIP_FIRST_CANDLES', 3))

        if self.direction_filter is None:
            self.direction_filter = os.getenv('DIRECTION_FILTER', 'Buy')

        if self.order_type is None:
            self.order_type = os.getenv('ORDER_TYPE', 'LIMIT')

        if self.product_type is None:
            self.product_type = os.getenv('PRODUCT_TYPE', 'MIS')

        if self.trading_mode is None:
            self.trading_mode = os.getenv('TRADING_MODE', 'TESTING').upper()

        # Handle watchlists
        if self.buy_watchlist is None:
            buy_watchlist_str = os.getenv('BUY_WATCHLIST', '')
            self.buy_watchlist = [s.strip() for s in buy_watchlist_str.split(',') if s.strip()] if buy_watchlist_str else []

        if self.sell_watchlist is None:
            sell_watchlist_str = os.getenv('SELL_WATCHLIST', '')
            self.sell_watchlist = [s.strip() for s in sell_watchlist_str.split(',') if s.strip()] if sell_watchlist_str else []
    
    @classmethod
    def from_env(cls):
        """Create configuration from environment variables"""
        return cls()
    
    def validate(self) -> bool:
        """Validate configuration settings"""
        errors = []

        # Validate risk management
        if self.per_trade_loss_limit <= 0:
            errors.append("PER_TRADE_LOSS_LIMIT must be positive")

        if self.max_trades_per_day <= 0:
            errors.append("MAX_TRADES_PER_DAY must be positive")

        # Validate API credentials
        if not self.api_key:
            errors.append("ZERODHA_API_KEY is required")

        if not self.api_secret:
            errors.append("ZERODHA_API_SECRET is required")

        # Validate watchlists
        if not self.buy_watchlist:
            errors.append("BUY_WATCHLIST cannot be empty")

        # Validate timeframe
        valid_timeframes = ["1minute", "3minute", "5minute", "15minute", "30minute", "60minute", "day"]
        if self.timeframe not in valid_timeframes:
            errors.append(f"TIMEFRAME must be one of {valid_timeframes}")

        # Validate trading mode
        valid_modes = ["TESTING", "LIVE"]
        if self.trading_mode not in valid_modes:
            errors.append(f"TRADING_MODE must be one of {valid_modes}")

        if errors:
            print("❌ Configuration validation errors:")
            for error in errors:
                print(f"  - {error}")
            return False

        print("✅ Configuration validation passed")
        return True
    

    
    def display_config(self):
        """Display current configuration"""
        print("\n" + "="*50)
        print("TRADING ALGORITHM CONFIGURATION")
        print("="*50)
        print(f"Per Trade Loss Limit: ₹{self.per_trade_loss_limit}")
        print(f"Max Trades Per Day: {self.max_trades_per_day}")
        print(f"Timeframe: {self.timeframe}")
        print(f"Trading Hours: {self.trading_start_time} - {self.trading_end_time}")
        print(f"Skip First Candles: {self.skip_first_candles}")
        print(f"Direction Filter: {self.direction_filter}")
        print(f"Order Type: {self.order_type}")
        print(f"Product Type: {self.product_type}")
        print(f"Trading Mode: {self.trading_mode}")
        print(f"\nBuy Watchlist ({len(self.buy_watchlist)} symbols):")
        for symbol in self.buy_watchlist:
            print(f"  - {symbol}")
        print(f"\nSell Watchlist ({len(self.sell_watchlist)} symbols):")
        for symbol in self.sell_watchlist:
            print(f"  - {symbol}")
        print("="*50)


if __name__ == "__main__":
    # Test configuration loading from environment
    print("Testing configuration loading from environment variables...")
    config = TradingConfig()
    config.display_config()
