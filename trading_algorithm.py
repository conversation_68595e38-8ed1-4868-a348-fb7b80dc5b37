"""
Main Trading Algorithm
Integrates all components into a comprehensive trading system
"""

import time
import signal
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
import threading
from config import TradingConfig
from zerodha_api import ZerodhaAPI
from candle_manager import CandleManager
from buy_logic import BuyLogicEngine
from order_manager import OrderManager

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_algorithm.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TradingAlgorithm:
    """Main trading algorithm class"""
    
    def __init__(self):
        """Initialize trading algorithm"""
        self.config = TradingConfig()
        self.running = False
        self.api = None
        self.candle_manager = None
        self.buy_logic = None
        self.order_manager = None
        
        # Algorithm settings
        self.scan_interval = 60  # seconds between scans
        self.last_scan_time = datetime.now()
        self.daily_reset_done = False
        
        # Performance tracking
        self.start_time = None
        self.total_signals_generated = 0
        self.total_orders_placed = 0
        
        # Initialize components
        self.initialize_components()
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def initialize_components(self) -> bool:
        """Initialize all trading components"""
        try:
            mode_icon = "🧪" if self.config.trading_mode == 'TESTING' else "🚨"
            logger.info(f"🚀 Initializing Trading Algorithm Components...")
            logger.info(f"{mode_icon} TRADING MODE: {self.config.trading_mode}")

            if self.config.trading_mode == 'TESTING':
                logger.info("📝 All orders will be SIMULATED - No real trades will be executed")
            else:
                logger.info("⚠️ LIVE MODE: Real orders will be executed with real money!")

            # Validate configuration
            if not self.config.validate():
                logger.error("❌ Configuration validation failed")
                return False
            
            # Initialize Zerodha API
            logger.info("📡 Connecting to Zerodha API...")
            self.api = ZerodhaAPI(self.config)
            if not self.api.connected:
                logger.error("❌ Failed to connect to Zerodha API")
                return False
            
            # Initialize Candle Manager
            logger.info("📊 Initializing Candle Manager...")
            self.candle_manager = CandleManager(self.config, self.api)
            
            # Initialize Buy Logic Engine
            logger.info("🎯 Initializing Buy Logic Engine...")
            self.buy_logic = BuyLogicEngine(self.config, self.api, self.candle_manager)
            
            # Initialize Order Manager
            logger.info("📝 Initializing Order Manager...")
            self.order_manager = OrderManager(self.config, self.api)
            
            logger.info("✅ All components initialized successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing components: {e}")
            return False
    
    def load_historical_data(self):
        """Load historical data for all watchlist symbols"""
        try:
            logger.info("📈 Loading historical data for watchlist symbols...")
            
            for symbol in self.config.buy_watchlist:
                success = self.candle_manager.fetch_historical_candles(symbol, days_back=1)
                if success:
                    logger.info(f"✅ Historical data loaded for {symbol}")
                else:
                    logger.warning(f"⚠️ Failed to load historical data for {symbol}")
                
                time.sleep(0.5)  # Avoid API rate limits
            
            logger.info("✅ Historical data loading completed")
            
        except Exception as e:
            logger.error(f"❌ Error loading historical data: {e}")
    
    def check_market_status(self) -> bool:
        """Check if market is open and algorithm should run"""
        try:
            if not self.api.is_market_open():
                return False
            
            # Check if it's a new trading day
            current_date = datetime.now().date()
            if not self.daily_reset_done:
                self.reset_daily_counters()
                self.daily_reset_done = True
                logger.info(f"🌅 New trading day started: {current_date}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error checking market status: {e}")
            return False
    
    def reset_daily_counters(self):
        """Reset daily counters and data"""
        try:
            # Reset buy logic counters
            self.buy_logic.reset_daily_counters()
            
            # Initialize daily data for candle manager
            self.candle_manager.initialize_daily_data()
            
            logger.info("🔄 Daily counters reset successfully")
            
        except Exception as e:
            logger.error(f"❌ Error resetting daily counters: {e}")
    
    def fetch_live_data(self):
        """Fetch live candle data for all watchlist symbols"""
        try:
            current_time = datetime.now()
            
            for symbol in self.config.buy_watchlist:
                try:
                    # Get current candle data
                    # Note: In a real implementation, you would fetch live candle data
                    # For now, we'll simulate by getting the latest historical data
                    
                    end_time = current_time
                    start_time = current_time - timedelta(minutes=5)
                    
                    # Convert timeframe
                    interval_map = {
                        "1minute": "minute",
                        "3minute": "3minute", 
                        "5minute": "5minute",
                        "15minute": "15minute",
                        "30minute": "30minute",
                        "60minute": "60minute"
                    }
                    
                    interval = interval_map.get(self.config.timeframe, "minute")
                    
                    df = self.api.get_historical_data(symbol, start_time, end_time, interval)
                    
                    if df is not None and not df.empty:
                        # Get the latest candle
                        latest_candle = df.iloc[-1]
                        
                        from candle_manager import CandleData
                        candle = CandleData(
                            timestamp=latest_candle.name,
                            open=latest_candle['open'],
                            high=latest_candle['high'],
                            low=latest_candle['low'],
                            close=latest_candle['close'],
                            volume=latest_candle['volume'],
                            symbol=symbol
                        )
                        
                        # Add candle to manager
                        self.candle_manager.add_candle(candle)
                    
                except Exception as e:
                    logger.error(f"❌ Error fetching live data for {symbol}: {e}")
                
                time.sleep(0.1)  # Small delay to avoid API rate limits
            
        except Exception as e:
            logger.error(f"❌ Error in fetch_live_data: {e}")
    
    def scan_and_execute(self):
        """Scan for signals and execute trades"""
        try:
            logger.info("🔍 Scanning for buy signals...")
            
            # Scan watchlist for buy signals
            signals = self.buy_logic.scan_watchlist_for_signals()
            
            if signals:
                logger.info(f"🎯 Found {len(signals)} buy signals")
                self.total_signals_generated += len(signals)
                
                # Execute signals through order manager
                for signal in signals:
                    order_id = self.order_manager.place_buy_order(signal)
                    if order_id:
                        self.total_orders_placed += 1
                        logger.info(f"✅ Order placed for {signal.symbol}: {order_id}")
                    
                    # Check if we've reached daily trade limit
                    if not self.buy_logic.can_place_trade():
                        logger.info("🛑 Daily trade limit reached")
                        break
            else:
                logger.debug("No buy signals found")
            
        except Exception as e:
            logger.error(f"❌ Error in scan_and_execute: {e}")
    
    def update_orders_and_positions(self):
        """Update order statuses and position tracking"""
        try:
            # Perform periodic updates
            self.order_manager.periodic_update()
            
        except Exception as e:
            logger.error(f"❌ Error updating orders and positions: {e}")
    
    def display_status(self):
        """Display current algorithm status"""
        try:
            current_time = datetime.now()
            runtime = current_time - self.start_time if self.start_time else timedelta(0)
            
            print(f"\n{'='*60}")
            print(f"TRADING ALGORITHM STATUS - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*60}")
            print(f"Runtime: {runtime}")
            print(f"Market Open: {self.api.is_market_open()}")
            print(f"Total Signals Generated: {self.total_signals_generated}")
            print(f"Total Orders Placed: {self.total_orders_placed}")
            
            # Display buy logic stats
            self.buy_logic.display_daily_stats()
            
            # Display order manager status
            self.order_manager.display_status()
            
            print(f"{'='*60}")
            
        except Exception as e:
            logger.error(f"❌ Error displaying status: {e}")
    
    def run(self):
        """Main algorithm loop"""
        try:
            logger.info("🚀 Starting Trading Algorithm...")
            self.running = True
            self.start_time = datetime.now()
            
            # Load historical data
            self.load_historical_data()
            
            # Main loop
            while self.running:
                try:
                    # Check market status
                    if not self.check_market_status():
                        logger.debug("Market is closed, waiting...")
                        time.sleep(60)  # Wait 1 minute before checking again
                        continue
                    
                    current_time = datetime.now()
                    
                    # Check if it's time for a new scan
                    if (current_time - self.last_scan_time).seconds >= self.scan_interval:
                        # Fetch live data
                        self.fetch_live_data()
                        
                        # Scan and execute
                        self.scan_and_execute()
                        
                        self.last_scan_time = current_time
                    
                    # Update orders and positions
                    self.update_orders_and_positions()
                    
                    # Display status every 5 minutes
                    if current_time.minute % 5 == 0 and current_time.second < 10:
                        self.display_status()
                    
                    # Sleep for a short interval
                    time.sleep(10)
                    
                except KeyboardInterrupt:
                    logger.info("🛑 Keyboard interrupt received")
                    break
                except Exception as e:
                    logger.error(f"❌ Error in main loop: {e}")
                    time.sleep(30)  # Wait before retrying
            
        except Exception as e:
            logger.error(f"❌ Fatal error in trading algorithm: {e}")
        finally:
            self.shutdown()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"🛑 Received signal {signum}, shutting down...")
        self.running = False
    
    def shutdown(self):
        """Graceful shutdown"""
        try:
            logger.info("🛑 Shutting down Trading Algorithm...")
            self.running = False
            
            # Display final status
            self.display_status()
            
            # Cancel any pending orders if needed
            open_orders = self.order_manager.get_open_orders()
            if open_orders:
                logger.info(f"⚠️ {len(open_orders)} open orders found during shutdown")
                # Optionally cancel them or leave them open
            
            logger.info("✅ Trading Algorithm shutdown completed")
            
        except Exception as e:
            logger.error(f"❌ Error during shutdown: {e}")


def main():
    """Main function to run the trading algorithm"""
    try:
        # Create and run trading algorithm
        algorithm = TradingAlgorithm()
        algorithm.run()
        
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
