"""
Zerodha API Integration Wrapper
Handles all interactions with Zerodha Kite API for live trading
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import time
import logging
from typing import Dict, List, Optional, Tuple
from kiteconnect import KiteConnect
from kiteconnect.exceptions import KiteException
from config import TradingConfig

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ZerodhaAPI:
    """Wrapper class for Zerodha Kite API operations"""
    
    def __init__(self, config: TradingConfig):
        """Initialize Zerodha API with configuration"""
        self.config = config
        self.kite = None
        self.instruments = {}
        self.instrument_tokens = {}
        self.connected = False
        
        # Initialize connection
        self.connect()
    
    def connect(self) -> bool:
        """Establish connection to Zerodha API"""
        try:
            self.kite = KiteConnect(api_key=self.config.api_key)
            
            if self.config.access_token:
                self.kite.set_access_token(self.config.access_token)
                
                # Test connection
                profile = self.kite.profile()
                logger.info(f"✅ Connected to Zerodha API - User: {profile['user_name']}")
                
                # Load instruments
                self.load_instruments()
                self.connected = True
                return True
            else:
                logger.error("❌ Access token not provided in configuration")
                return False
                
        except KiteException as e:
            logger.error(f"❌ Zerodha API connection failed: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ Unexpected error during connection: {e}")
            return False
    
    def load_instruments(self):
        """Load instrument data for symbol lookup"""
        try:
            instruments_list = self.kite.instruments("NSE")
            
            for instrument in instruments_list:
                symbol = instrument['tradingsymbol']
                self.instruments[symbol] = instrument
                self.instrument_tokens[symbol] = instrument['instrument_token']
            
            logger.info(f"✅ Loaded {len(self.instruments)} instruments")
            
        except Exception as e:
            logger.error(f"❌ Failed to load instruments: {e}")
    
    def get_instrument_token(self, symbol: str) -> Optional[int]:
        """Get instrument token for a symbol"""
        return self.instrument_tokens.get(symbol)
    
    def get_historical_data(self, symbol: str, from_date: datetime, to_date: datetime, 
                          interval: str = "minute") -> Optional[pd.DataFrame]:
        """Fetch historical candle data"""
        try:
            instrument_token = self.get_instrument_token(symbol)
            if not instrument_token:
                logger.error(f"❌ Instrument token not found for {symbol}")
                return None
            
            # Fetch data from Zerodha
            data = self.kite.historical_data(
                instrument_token=instrument_token,
                from_date=from_date,
                to_date=to_date,
                interval=interval
            )
            
            if not data:
                logger.warning(f"⚠️ No data received for {symbol}")
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame(data)
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
            
            # Add calculated fields
            df['candle_length'] = df['high'] - df['low']
            df['is_bearish'] = df['close'] < df['open']
            
            logger.info(f"✅ Fetched {len(df)} candles for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch historical data for {symbol}: {e}")
            return None
    
    def get_live_price(self, symbol: str) -> Optional[Dict]:
        """Get current live price for a symbol"""
        try:
            instrument_token = self.get_instrument_token(symbol)
            if not instrument_token:
                return None
            
            quote = self.kite.quote([f"NSE:{symbol}"])
            return quote.get(f"NSE:{symbol}")
            
        except Exception as e:
            logger.error(f"❌ Failed to get live price for {symbol}: {e}")
            return None
    
    def place_order(self, symbol: str, transaction_type: str, quantity: int, 
                   price: float = None, order_type: str = "LIMIT", 
                   product: str = "MIS") -> Optional[str]:
        """Place a buy/sell order"""
        try:
            order_params = {
                'tradingsymbol': symbol,
                'exchange': 'NSE',
                'transaction_type': transaction_type,  # BUY or SELL
                'quantity': quantity,
                'order_type': order_type,  # LIMIT or MARKET
                'product': product,  # MIS, CNC, NRML
            }
            
            if order_type == "LIMIT" and price:
                order_params['price'] = price
            
            order_id = self.kite.place_order(**order_params)
            
            logger.info(f"✅ Order placed - {transaction_type} {quantity} {symbol} at ₹{price}")
            logger.info(f"Order ID: {order_id}")
            
            return order_id
            
        except Exception as e:
            logger.error(f"❌ Failed to place order for {symbol}: {e}")
            return None
    
    def get_order_status(self, order_id: str) -> Optional[Dict]:
        """Get status of a specific order"""
        try:
            orders = self.kite.orders()
            for order in orders:
                if order['order_id'] == order_id:
                    return order
            return None
            
        except Exception as e:
            logger.error(f"❌ Failed to get order status for {order_id}: {e}")
            return None
    
    def get_positions(self) -> Optional[List[Dict]]:
        """Get current positions"""
        try:
            positions = self.kite.positions()
            return positions['day']  # Return day positions
            
        except Exception as e:
            logger.error(f"❌ Failed to get positions: {e}")
            return None
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        try:
            self.kite.cancel_order(order_id=order_id)
            logger.info(f"✅ Order {order_id} cancelled successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to cancel order {order_id}: {e}")
            return False
    
    def modify_order(self, order_id: str, price: float = None, 
                    quantity: int = None) -> bool:
        """Modify an existing order"""
        try:
            modify_params = {'order_id': order_id}
            
            if price:
                modify_params['price'] = price
            if quantity:
                modify_params['quantity'] = quantity
            
            self.kite.modify_order(**modify_params)
            logger.info(f"✅ Order {order_id} modified successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to modify order {order_id}: {e}")
            return False
    
    def get_funds(self) -> Optional[Dict]:
        """Get account funds information"""
        try:
            margins = self.kite.margins()
            return margins['equity']
            
        except Exception as e:
            logger.error(f"❌ Failed to get funds: {e}")
            return None
    
    def calculate_position_size(self, risk_amount: float, entry_price: float, 
                              stop_loss_price: float) -> int:
        """Calculate position size based on risk management"""
        try:
            risk_per_share = abs(entry_price - stop_loss_price)
            if risk_per_share == 0:
                return 0
            
            quantity = int(risk_amount / risk_per_share)
            
            # Ensure minimum quantity is 1
            return max(1, quantity)
            
        except Exception as e:
            logger.error(f"❌ Failed to calculate position size: {e}")
            return 0
    
    def is_market_open(self) -> bool:
        """Check if market is currently open"""
        try:
            now = datetime.now()
            current_time = now.strftime("%H:%M")
            
            # Check if it's a weekday (Monday=0, Sunday=6)
            if now.weekday() >= 5:  # Saturday or Sunday
                return False
            
            # Check trading hours
            start_time = self.config.trading_start_time
            end_time = self.config.trading_end_time
            
            return start_time <= current_time <= end_time
            
        except Exception as e:
            logger.error(f"❌ Failed to check market status: {e}")
            return False


def test_zerodha_connection():
    """Test function to verify Zerodha API connection"""
    # Load configuration from environment
    config = TradingConfig()

    # Test connection
    api = ZerodhaAPI(config)

    if api.connected:
        print("✅ Zerodha API connection successful!")

        # Test getting live price
        if config.buy_watchlist:
            symbol = config.buy_watchlist[0]
            price = api.get_live_price(symbol)
            if price:
                print(f"✅ Live price for {symbol}: ₹{price['last_price']}")

        # Test getting funds
        funds = api.get_funds()
        if funds:
            print(f"✅ Available funds: ₹{funds['available']['live_balance']}")
    else:
        print("❌ Zerodha API connection failed!")


if __name__ == "__main__":
    test_zerodha_connection()
