from kiteconnect import KiteConnect
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API credentials from environment variables
api_key = os.getenv('ZERODHA_API_KEY')
api_secret = os.getenv('ZERODHA_API_SECRET')

if not api_key or not api_secret:
    print("❌ API credentials not found in environment variables")
    print("Please set ZERODHA_API_KEY and ZERODHA_API_SECRET in your .env file")
    print("Run: python3 env_setup.py to configure environment variables")
    exit(1)

kite = KiteConnect(api_key=api_key)

# Step 1: Print the login URL to authenticate
print("🔐 Zerodha Login URL:")
print("="*50)
print(kite.login_url())
print("="*50)
print("\n📝 Instructions:")
print("1. Click the URL above or copy-paste it in your browser")
print("2. Login with your Zerodha credentials")
print("3. After successful login, copy the 'request_token' from the redirected URL")
print("4. Update the request_token in generate_token.py and run it")
