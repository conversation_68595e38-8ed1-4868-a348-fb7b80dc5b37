"""
Buy Logic Engine
Implements the core buy logic based on volume tracking and bearish candle identification
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass
from config import TradingConfig
from zerodha_api import ZerodhaAPI
from candle_manager import CandleManager, CandleData

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class BuySignal:
    """Data class to represent a buy signal"""
    symbol: str
    timestamp: datetime
    entry_price: float  # High of the candle
    stop_loss_price: float  # Low of the candle
    candle_data: CandleData
    quantity: int
    risk_amount: float
    signal_strength: float = 1.0
    
    def __str__(self):
        return (f"BUY SIGNAL: {self.symbol} at ₹{self.entry_price} "
                f"(SL: ₹{self.stop_loss_price}, Qty: {self.quantity})")

class BuyLogicEngine:
    """Core buy logic engine implementing the trading strategy"""
    
    def __init__(self, config: TradingConfig, api: ZerodhaAPI, candle_manager: CandleManager):
        """Initialize buy logic engine"""
        self.config = config
        self.api = api
        self.candle_manager = candle_manager
        self.daily_trade_count = 0
        self.daily_trades: Dict[str, int] = {}  # Track trades per symbol
        self.active_signals: List[BuySignal] = []
        
        # Reset daily counters at start
        self.reset_daily_counters()
    
    def reset_daily_counters(self):
        """Reset daily trade counters"""
        self.daily_trade_count = 0
        self.daily_trades = {}
        logger.info("🔄 Daily trade counters reset")
    
    def can_place_trade(self) -> bool:
        """Check if we can place more trades today"""
        return self.daily_trade_count < self.config.max_trades_per_day
    
    def check_buy_conditions(self, symbol: str) -> Optional[BuySignal]:
        """
        Check all buy conditions for a symbol and return buy signal if conditions are met

        Buy Logic:
        1. Skip first 3 candles of the day for order placement
        2. Track lowest volume candle from ALL candles (including skipped ones)
        3. Candle must be bearish (close < open)
        4. Direction filter must be set to "Buy"
        5. Current candle must be the lowest-volume candle of the day
        6. Current candle must not be in the skipped range (no orders for first 3 candles)
        """
        
        try:
            # Check if we can place more trades
            if not self.can_place_trade():
                logger.debug(f"❌ Max daily trades ({self.config.max_trades_per_day}) reached")
                return None
            
            # Check direction filter
            if self.config.direction_filter.lower() != "buy":
                logger.debug(f"❌ Direction filter is not set to 'Buy' for {symbol}")
                return None
            
            # Get current candle
            current_candle = self.candle_manager.get_current_candle(symbol)
            if not current_candle:
                logger.debug(f"❌ No current candle data for {symbol}")
                return None
            
            # Check if first three candles are processed
            today = datetime.now().date().strftime("%Y-%m-%d")
            day_data = self.candle_manager.get_day_data(symbol, today)
            
            if not day_data or not day_data.first_three_candles_processed:
                logger.debug(f"❌ First {self.config.skip_first_candles} candles not yet processed for {symbol}")
                return None
            
            # Condition 1: Candle must be bearish (close < open)
            if not current_candle.is_bearish:
                logger.debug(f"❌ Current candle is not bearish for {symbol}")
                return None
            
            # Condition 2: Must be the lowest-volume candle of the day
            if not self.candle_manager.is_current_candle_lowest_volume(symbol):
                logger.debug(f"❌ Current candle is not the lowest volume candle for {symbol}")
                return None

            # Condition 3: Current candle must be eligible for trading (not in skipped range)
            if not self.candle_manager.is_current_candle_eligible_for_trading(symbol):
                logger.debug(f"❌ Current candle is in skipped range and not eligible for trading for {symbol}")
                return None
            
            # All conditions met - generate buy signal
            entry_price = current_candle.high
            stop_loss_price = current_candle.low
            
            # Calculate position size based on risk management
            quantity = self.calculate_position_size(
                risk_amount=self.config.per_trade_loss_limit,
                entry_price=entry_price,
                stop_loss_price=stop_loss_price
            )
            
            if quantity <= 0:
                logger.warning(f"⚠️ Calculated quantity is 0 for {symbol}")
                return None
            
            # Create buy signal
            buy_signal = BuySignal(
                symbol=symbol,
                timestamp=current_candle.timestamp,
                entry_price=entry_price,
                stop_loss_price=stop_loss_price,
                candle_data=current_candle,
                quantity=quantity,
                risk_amount=self.config.per_trade_loss_limit
            )
            
            logger.info(f"🎯 BUY SIGNAL GENERATED: {buy_signal}")
            return buy_signal
            
        except Exception as e:
            logger.error(f"❌ Error checking buy conditions for {symbol}: {e}")
            return None
    
    def calculate_position_size(self, risk_amount: float, entry_price: float, 
                              stop_loss_price: float) -> int:
        """
        Calculate position size based on risk management
        Quantity = risk_amount / candle_length
        Where candle_length = high - low = entry_price - stop_loss_price
        """
        try:
            candle_length = abs(entry_price - stop_loss_price)
            
            if candle_length == 0:
                logger.warning("⚠️ Candle length is 0, cannot calculate position size")
                return 0
            
            quantity = int(risk_amount / candle_length)
            
            # Ensure minimum quantity is 1
            quantity = max(1, quantity)
            
            logger.info(f"📊 Position Size Calculation:")
            logger.info(f"  Risk Amount: ₹{risk_amount}")
            logger.info(f"  Entry Price: ₹{entry_price}")
            logger.info(f"  Stop Loss: ₹{stop_loss_price}")
            logger.info(f"  Candle Length: ₹{candle_length}")
            logger.info(f"  Calculated Quantity: {quantity}")
            
            return quantity
            
        except Exception as e:
            logger.error(f"❌ Error calculating position size: {e}")
            return 0
    
    def validate_buy_signal(self, signal: BuySignal) -> bool:
        """Validate buy signal before execution"""
        try:
            # Check if market is open
            if not self.api.is_market_open():
                logger.warning("⚠️ Market is closed, cannot place order")
                return False
            
            # Check available funds
            funds = self.api.get_funds()
            if funds:
                required_amount = signal.entry_price * signal.quantity
                available_balance = funds.get('available', {}).get('live_balance', 0)
                
                if required_amount > available_balance:
                    logger.warning(f"⚠️ Insufficient funds. Required: ₹{required_amount}, Available: ₹{available_balance}")
                    return False
            
            # Check if we already have a position in this symbol
            positions = self.api.get_positions()
            if positions:
                for position in positions:
                    if (position['tradingsymbol'] == signal.symbol and 
                        position['quantity'] != 0):
                        logger.warning(f"⚠️ Already have position in {signal.symbol}")
                        return False
            
            # Validate price levels
            if signal.entry_price <= signal.stop_loss_price:
                logger.warning(f"⚠️ Invalid price levels: Entry {signal.entry_price} <= SL {signal.stop_loss_price}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error validating buy signal: {e}")
            return False
    
    def execute_buy_signal(self, signal: BuySignal) -> bool:
        """Execute a buy signal by placing orders"""
        try:
            # Validate signal before execution
            if not self.validate_buy_signal(signal):
                return False
            
            # Place buy order at the high of the candle
            buy_order_id = self.api.place_order(
                symbol=signal.symbol,
                transaction_type="BUY",
                quantity=signal.quantity,
                price=signal.entry_price,
                order_type=self.config.order_type,
                product=self.config.product_type
            )
            
            if not buy_order_id:
                logger.error(f"❌ Failed to place buy order for {signal.symbol}")
                return False
            
            logger.info(f"✅ Buy order placed successfully!")
            logger.info(f"  Order ID: {buy_order_id}")
            logger.info(f"  Symbol: {signal.symbol}")
            logger.info(f"  Quantity: {signal.quantity}")
            logger.info(f"  Price: ₹{signal.entry_price}")
            
            # Update trade counters
            self.daily_trade_count += 1
            self.daily_trades[signal.symbol] = self.daily_trades.get(signal.symbol, 0) + 1
            
            # Add to active signals for tracking
            self.active_signals.append(signal)
            
            # Note: Stop loss order will be placed after buy order is executed
            # This should be handled in the order management system
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error executing buy signal for {signal.symbol}: {e}")
            return False
    
    def scan_watchlist_for_signals(self) -> List[BuySignal]:
        """Scan all symbols in buy watchlist for buy signals"""
        signals = []
        
        logger.info(f"🔍 Scanning {len(self.config.buy_watchlist)} symbols for buy signals...")
        
        for symbol in self.config.buy_watchlist:
            try:
                signal = self.check_buy_conditions(symbol)
                if signal:
                    signals.append(signal)
                    logger.info(f"✅ Buy signal found for {symbol}")
                
            except Exception as e:
                logger.error(f"❌ Error scanning {symbol}: {e}")
        
        logger.info(f"🎯 Found {len(signals)} buy signals")
        return signals
    
    def process_signals(self, signals: List[BuySignal]) -> int:
        """Process and execute buy signals"""
        executed_count = 0
        
        for signal in signals:
            if self.execute_buy_signal(signal):
                executed_count += 1
            
            # Check if we've reached daily trade limit
            if not self.can_place_trade():
                logger.info(f"🛑 Daily trade limit reached ({self.config.max_trades_per_day})")
                break
        
        return executed_count
    
    def get_daily_stats(self) -> Dict:
        """Get daily trading statistics"""
        return {
            'total_trades': self.daily_trade_count,
            'max_trades': self.config.max_trades_per_day,
            'remaining_trades': self.config.max_trades_per_day - self.daily_trade_count,
            'trades_per_symbol': self.daily_trades.copy(),
            'active_signals': len(self.active_signals)
        }
    
    def display_daily_stats(self):
        """Display daily trading statistics"""
        stats = self.get_daily_stats()
        
        print(f"\n📈 Daily Trading Statistics")
        print("="*40)
        print(f"Total Trades: {stats['total_trades']}/{stats['max_trades']}")
        print(f"Remaining Trades: {stats['remaining_trades']}")
        print(f"Active Signals: {stats['active_signals']}")
        
        if stats['trades_per_symbol']:
            print(f"\nTrades per Symbol:")
            for symbol, count in stats['trades_per_symbol'].items():
                print(f"  {symbol}: {count}")
        
        print("="*40)


def test_buy_logic():
    """Test function for buy logic engine"""
    from config import TradingConfig

    # Load configuration from environment
    config = TradingConfig()

    # Initialize components
    api = ZerodhaAPI(config)
    candle_manager = CandleManager(config, api)
    buy_logic = BuyLogicEngine(config, api, candle_manager)

    # Test scanning for signals
    signals = buy_logic.scan_watchlist_for_signals()

    print(f"Found {len(signals)} buy signals")
    for signal in signals:
        print(signal)

    # Display stats
    buy_logic.display_daily_stats()


if __name__ == "__main__":
    test_buy_logic()
