"""
Candle Data Management System
Tracks daily candles, identifies lowest volume candles, and manages candle data for buy logic
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging
from dataclasses import dataclass
from config import TradingConfig
from zerodha_api import ZerodhaAPI

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class CandleData:
    """Data class to represent a single candle"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    symbol: str
    
    @property
    def is_bearish(self) -> bool:
        """Check if candle is bearish (close < open)"""
        return self.close < self.open
    
    @property
    def candle_length(self) -> float:
        """Calculate candle length (high - low)"""
        return self.high - self.low
    
    def __str__(self):
        return f"{self.symbol} {self.timestamp}: O:{self.open} H:{self.high} L:{self.low} C:{self.close} V:{self.volume}"

@dataclass
class DayData:
    """Data class to track daily candle information"""
    date: datetime
    symbol: str
    candles: List[CandleData]
    lowest_volume_candle: Optional[CandleData] = None
    lowest_volume_index: int = -1
    first_three_candles_processed: bool = False
    
    def add_candle(self, candle: CandleData):
        """Add a new candle to the day's data"""
        self.candles.append(candle)
    
    def get_candle_count(self) -> int:
        """Get total number of candles for the day"""
        return len(self.candles)
    
    def is_first_three_candles_complete(self, skip_count: int = 3) -> bool:
        """Check if first N candles are complete"""
        return len(self.candles) > skip_count

class CandleManager:
    """Manages candle data for multiple symbols across trading days"""
    
    def __init__(self, config: TradingConfig, api: ZerodhaAPI):
        """Initialize candle manager"""
        self.config = config
        self.api = api
        self.daily_data: Dict[str, Dict[str, DayData]] = {}  # {symbol: {date: DayData}}
        self.current_candles: Dict[str, CandleData] = {}  # Current live candles
        
        # Initialize daily data for watchlist symbols
        self.initialize_daily_data()
    
    def initialize_daily_data(self):
        """Initialize daily data structures for all watchlist symbols"""
        today = datetime.now().date()
        
        for symbol in self.config.buy_watchlist:
            if symbol not in self.daily_data:
                self.daily_data[symbol] = {}
            
            date_key = today.strftime("%Y-%m-%d")
            if date_key not in self.daily_data[symbol]:
                self.daily_data[symbol][date_key] = DayData(
                    date=today,
                    symbol=symbol,
                    candles=[]
                )
        
        logger.info(f"✅ Initialized daily data for {len(self.config.buy_watchlist)} symbols")
    
    def fetch_historical_candles(self, symbol: str, days_back: int = 1) -> bool:
        """Fetch historical candles for a symbol"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Convert timeframe for API call
            interval_map = {
                "1minute": "minute",
                "3minute": "3minute", 
                "5minute": "5minute",
                "15minute": "15minute",
                "30minute": "30minute",
                "60minute": "60minute"
            }
            
            interval = interval_map.get(self.config.timeframe, "minute")
            
            df = self.api.get_historical_data(symbol, start_date, end_date, interval)
            
            if df is None or df.empty:
                logger.warning(f"⚠️ No historical data for {symbol}")
                return False
            
            # Process each candle
            for timestamp, row in df.iterrows():
                candle = CandleData(
                    timestamp=timestamp,
                    open=row['open'],
                    high=row['high'],
                    low=row['low'],
                    close=row['close'],
                    volume=row['volume'],
                    symbol=symbol
                )
                
                self.add_candle(candle)
            
            logger.info(f"✅ Loaded {len(df)} historical candles for {symbol}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to fetch historical candles for {symbol}: {e}")
            return False
    
    def add_candle(self, candle: CandleData):
        """Add a new candle and update daily tracking"""
        symbol = candle.symbol
        date_key = candle.timestamp.date().strftime("%Y-%m-%d")
        
        # Ensure symbol and date exist in daily_data
        if symbol not in self.daily_data:
            self.daily_data[symbol] = {}
        
        if date_key not in self.daily_data[symbol]:
            self.daily_data[symbol][date_key] = DayData(
                date=candle.timestamp.date(),
                symbol=symbol,
                candles=[]
            )
        
        day_data = self.daily_data[symbol][date_key]
        day_data.add_candle(candle)
        
        # Update current candle
        self.current_candles[symbol] = candle
        
        # Update lowest volume tracking
        self.update_lowest_volume_candle(symbol, date_key)
        
        logger.debug(f"Added candle for {symbol}: {candle}")
    
    def update_lowest_volume_candle(self, symbol: str, date_key: str):
        """Update the lowest volume candle for a symbol on a specific date"""
        day_data = self.daily_data[symbol][date_key]

        # Skip first N candles as per configuration
        if not day_data.is_first_three_candles_complete(self.config.skip_first_candles):
            return

        # Mark first three candles as processed
        if not day_data.first_three_candles_processed:
            day_data.first_three_candles_processed = True
            logger.info(f"✅ First {self.config.skip_first_candles} candles processed for {symbol}")

        # Consider ALL candles for volume comparison (including skipped ones)
        # This ensures we track the true lowest volume candle of the day
        all_candles = day_data.candles

        if not all_candles:
            return

        # Find the candle with lowest volume from ALL candles
        lowest_volume_candle = min(all_candles, key=lambda c: c.volume)

        # Update if this is a new lowest or first time setting
        if (day_data.lowest_volume_candle is None or
            lowest_volume_candle.volume < day_data.lowest_volume_candle.volume):

            day_data.lowest_volume_candle = lowest_volume_candle
            day_data.lowest_volume_index = day_data.candles.index(lowest_volume_candle)

            logger.info(f"🔄 New lowest volume candle for {symbol}: "
                       f"Volume={lowest_volume_candle.volume} at {lowest_volume_candle.timestamp}")
    
    def is_current_candle_lowest_volume(self, symbol: str) -> bool:
        """Check if current candle is the lowest volume candle of the day"""
        today = datetime.now().date().strftime("%Y-%m-%d")

        if (symbol not in self.daily_data or
            today not in self.daily_data[symbol]):
            return False

        day_data = self.daily_data[symbol][today]
        current_candle = self.current_candles.get(symbol)

        if not current_candle or not day_data.lowest_volume_candle:
            return False

        # Check if current candle is the same as lowest volume candle
        return (current_candle.timestamp == day_data.lowest_volume_candle.timestamp and
                current_candle.volume == day_data.lowest_volume_candle.volume)

    def is_current_candle_eligible_for_trading(self, symbol: str) -> bool:
        """
        Check if current candle is eligible for trading (not in skipped range)
        This prevents orders from being placed for the first N candles
        """
        today = datetime.now().date().strftime("%Y-%m-%d")

        if (symbol not in self.daily_data or
            today not in self.daily_data[symbol]):
            return False

        day_data = self.daily_data[symbol][today]
        current_candle = self.current_candles.get(symbol)

        if not current_candle:
            return False

        # Find the index of current candle in today's candles
        try:
            current_candle_index = day_data.candles.index(current_candle)
        except ValueError:
            # Current candle not found in today's candles
            return False

        # Check if current candle is beyond the skip range
        return current_candle_index >= self.config.skip_first_candles
    
    def get_current_candle(self, symbol: str) -> Optional[CandleData]:
        """Get the current candle for a symbol"""
        return self.current_candles.get(symbol)
    
    def get_day_data(self, symbol: str, date: str = None) -> Optional[DayData]:
        """Get day data for a symbol"""
        if date is None:
            date = datetime.now().date().strftime("%Y-%m-%d")
        
        return self.daily_data.get(symbol, {}).get(date)
    
    def get_lowest_volume_candle(self, symbol: str, date: str = None) -> Optional[CandleData]:
        """Get the lowest volume candle for a symbol on a specific date"""
        day_data = self.get_day_data(symbol, date)
        return day_data.lowest_volume_candle if day_data else None
    
    def is_candle_bearish(self, symbol: str) -> bool:
        """Check if current candle is bearish"""
        current_candle = self.get_current_candle(symbol)
        return current_candle.is_bearish if current_candle else False
    
    def get_candle_stats(self, symbol: str, date: str = None) -> Dict:
        """Get statistics for candles of a symbol on a specific date"""
        day_data = self.get_day_data(symbol, date)
        
        if not day_data or not day_data.candles:
            return {}
        
        candles = day_data.candles
        volumes = [c.volume for c in candles]
        
        stats = {
            'total_candles': len(candles),
            'min_volume': min(volumes),
            'max_volume': max(volumes),
            'avg_volume': sum(volumes) / len(volumes),
            'lowest_volume_candle_index': day_data.lowest_volume_index,
            'first_three_processed': day_data.first_three_candles_processed
        }
        
        return stats
    
    def display_day_summary(self, symbol: str, date: str = None):
        """Display summary of the day's candle data"""
        if date is None:
            date = datetime.now().date().strftime("%Y-%m-%d")
        
        day_data = self.get_day_data(symbol, date)
        
        if not day_data:
            print(f"❌ No data found for {symbol} on {date}")
            return
        
        stats = self.get_candle_stats(symbol, date)
        
        print(f"\n📊 Day Summary for {symbol} ({date})")
        print("="*50)
        print(f"Total Candles: {stats['total_candles']}")
        print(f"Volume Range: {stats['min_volume']} - {stats['max_volume']}")
        print(f"Average Volume: {stats['avg_volume']:.0f}")
        print(f"First {self.config.skip_first_candles} Candles Processed: {stats['first_three_processed']}")
        
        if day_data.lowest_volume_candle:
            lv_candle = day_data.lowest_volume_candle
            print(f"Lowest Volume Candle: Index {stats['lowest_volume_candle_index']}")
            print(f"  Time: {lv_candle.timestamp}")
            print(f"  Volume: {lv_candle.volume}")
            print(f"  Price: O:{lv_candle.open} H:{lv_candle.high} L:{lv_candle.low} C:{lv_candle.close}")
            print(f"  Bearish: {lv_candle.is_bearish}")
        
        print("="*50)


def test_candle_manager():
    """Test function for candle manager"""
    from config import TradingConfig

    # Load configuration from environment
    config = TradingConfig()

    # Initialize API and candle manager
    api = ZerodhaAPI(config)
    candle_manager = CandleManager(config, api)

    if config.buy_watchlist:
        symbol = config.buy_watchlist[0]

        # Fetch historical data
        candle_manager.fetch_historical_candles(symbol, days_back=1)

        # Display summary
        candle_manager.display_day_summary(symbol)


if __name__ == "__main__":
    test_candle_manager()
