# Trading Algorithm - Zerodha Integration

A comprehensive trading algorithm that implements volume-based buy logic with automatic order placement and risk management using Zerodha's Kite API.

## Features

### 🎯 Core Trading Logic
- **Volume-based Buy Signals**: Tracks lowest volume candles after skipping first 3 candles of the day
- **Bearish Candle Filter**: Only triggers on bearish candles (close < open)
- **Dynamic Position Sizing**: Calculates quantity based on risk per trade and candle length
- **Automatic Stop-Loss**: Places stop-loss orders at candle low after buy execution

### 🛡️ Risk Management
- **Per Trade Loss Limit**: Configurable maximum loss per trade
- **Daily Trade Limit**: Maximum number of trades per day
- **Real-time P&L Tracking**: Monitors profit/loss across all positions
- **Fund Validation**: Checks available balance before placing orders

### 📊 Market Data Management
- **Live Data Integration**: Fetches real-time candle data from Zerodha
- **Historical Data Loading**: Loads past candles for analysis
- **Multiple Timeframes**: Supports 1min, 3min, 5min, 15min, 30min, 60min
- **Watchlist Management**: Separate buy and sell watchlists

### 🔧 Configuration System
- **Dynamic Settings**: All parameters configurable via JSON file
- **Watchlist Management**: Easy addition/removal of symbols
- **Trading Hours**: Configurable market hours
- **API Credentials**: Secure credential management

## Installation

1. **Clone or download the files**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

## Setup

### 1. Environment Configuration
Configure your environment variables using the secure .env system:
```bash
python3 env_setup.py
```

This will guide you through:
- Zerodha API credentials (stored securely in .env file)
- Risk management settings (loss limits, max trades)
- Trading timeframe and hours
- Watchlist configuration

### 2. Zerodha API Setup
1. **Get API credentials** from Zerodha Kite Connect
2. **Set up environment variables**:
   ```bash
   python3 env_setup.py
   # Choose option 1: Setup Environment Variables
   ```
3. **Generate login URL**:
   ```bash
   python3 zerodha_login.py
   ```
4. **Generate access token**:
   ```bash
   python3 generate_token.py
   ```
   Enter the request_token from the login URL

### 3. Test Connection
Verify your setup:
```bash
python3 env_setup.py
# Choose option 5: Validate Configuration
```

## Usage

### Running the Algorithm
Start the main trading algorithm:
```bash
python trading_algorithm.py
```

The algorithm will:
1. Load historical data for watchlist symbols
2. Monitor live market data during trading hours
3. Scan for buy signals based on volume analysis
4. Place buy orders with automatic stop-loss
5. Track positions and P&L in real-time

### Configuration Management
- **View current config**: Run `python3 env_setup.py` → option 2
- **Update settings**: Run `python3 env_setup.py` → option 1
- **Update access token**: Run `python3 env_setup.py` → option 3

## Trading Logic Details

### Buy Signal Conditions
1. **Skip First 3 Candles**: Algorithm ignores first 3 candles of the day
2. **Lowest Volume Tracking**: Identifies candle with lowest volume after first 3
3. **Bearish Candle**: Current candle must be bearish (close < open)
4. **Direction Filter**: Must be set to "Buy" in configuration
5. **Volume Confirmation**: Current candle must be the lowest volume candle of the day

### Position Sizing
```
Quantity = Risk Amount / Candle Length
Where Candle Length = High - Low
```

### Order Execution
1. **Buy Order**: Placed at the high of the signal candle
2. **Stop-Loss**: Automatically placed at the low of the signal candle
3. **Order Type**: Configurable (LIMIT/MARKET)
4. **Product Type**: Configurable (MIS/CNC/NRML)

## File Structure

```
├── config.py              # Configuration management
├── zerodha_api.py         # Zerodha API wrapper
├── candle_manager.py      # Candle data management
├── buy_logic.py           # Buy signal logic
├── order_manager.py       # Order and position management
├── trading_algorithm.py   # Main algorithm
├── env_setup.py           # Environment variables setup
├── requirements.txt       # Dependencies
├── zerodha_login.py       # Login helper
├── generate_token.py      # Token generation
├── .env                   # Environment variables (not in git)
├── .env.example           # Environment variables template
├── .gitignore             # Git ignore file
└── README.md             # This file
```

## Configuration Options

### Risk Management
- `per_trade_loss_limit`: Maximum loss per trade (₹)
- `max_trades_per_day`: Maximum trades per day

### Watchlists
- `buy_watchlist`: Symbols to monitor for buy signals
- `sell_watchlist`: Symbols to monitor for sell signals

### Trading Settings
- `timeframe`: Candle timeframe (1minute, 3minute, etc.)
- `trading_start_time`: Market start time (09:15)
- `trading_end_time`: Market end time (15:30)
- `skip_first_candles`: Number of first candles to skip (3)

### Environment Variables (.env file)
- `ZERODHA_API_KEY`: Zerodha API key
- `ZERODHA_API_SECRET`: Zerodha API secret
- `ZERODHA_ACCESS_TOKEN`: Generated access token
- `PER_TRADE_LOSS_LIMIT`: Maximum loss per trade (₹)
- `MAX_TRADES_PER_DAY`: Maximum trades per day
- `BUY_WATCHLIST`: Comma-separated buy symbols
- `SELL_WATCHLIST`: Comma-separated sell symbols
- `TIMEFRAME`: Candle timeframe
- `ORDER_TYPE`: Order type (LIMIT/MARKET)
- `PRODUCT_TYPE`: Product type (MIS/CNC/NRML)

### Order Settings
- `order_type`: LIMIT or MARKET
- `product_type`: MIS, CNC, or NRML

## Logging

The algorithm creates detailed logs in:
- `trading_algorithm.log`: Main algorithm log
- Console output: Real-time status updates

## Safety Features

- **Market Hours Check**: Only trades during configured hours
- **Fund Validation**: Verifies sufficient balance before orders
- **Position Limits**: Prevents multiple positions in same symbol
- **Graceful Shutdown**: Handles interrupts safely
- **Error Recovery**: Continues operation despite individual errors

## Important Notes

⚠️ **Risk Warning**: This is a trading algorithm that places real orders with real money. Always:
- Test thoroughly in paper trading mode first
- Start with small position sizes
- Monitor the algorithm closely
- Understand the risks involved

⚠️ **API Limits**: Zerodha has API rate limits. The algorithm includes delays to respect these limits.

⚠️ **Market Data**: Ensure you have proper market data subscriptions for the symbols you're trading.

## Support

For issues or questions:
1. Check the logs for error messages
2. Verify API credentials and connection
3. Ensure market data subscriptions are active
4. Test with a single symbol first

## License

This code is provided for educational purposes. Use at your own risk.
