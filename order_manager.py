"""
Order Management System
Handles order placement, tracking, and stop-loss management
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import time
from dataclasses import dataclass, field
from enum import Enum
from config import TradingConfig
from zerodha_api import ZerodhaAPI
from buy_logic import BuySignal

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    OPEN = "OPEN"
    COMPLETE = "COMPLETE"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"

class OrderType(Enum):
    """Order type enumeration"""
    BUY = "BUY"
    SELL = "SELL"
    STOP_LOSS = "STOP_LOSS"

@dataclass
class Order:
    """Data class to represent an order"""
    order_id: str
    symbol: str
    order_type: OrderType
    quantity: int
    price: float
    status: OrderStatus
    timestamp: datetime
    parent_order_id: Optional[str] = None  # For stop-loss orders
    executed_price: Optional[float] = None
    executed_quantity: int = 0
    executed_time: Optional[datetime] = None
    
    def __str__(self):
        return (f"{self.order_type.value} {self.symbol}: "
                f"{self.quantity}@₹{self.price} [{self.status.value}]")

@dataclass
class Position:
    """Data class to represent a trading position"""
    symbol: str
    quantity: int
    entry_price: float
    current_price: float
    stop_loss_price: float
    buy_order: Order
    stop_loss_order: Optional[Order] = None
    pnl: float = 0.0
    entry_time: datetime = field(default_factory=datetime.now)
    
    def update_pnl(self, current_price: float):
        """Update P&L based on current price"""
        self.current_price = current_price
        self.pnl = (current_price - self.entry_price) * self.quantity
    
    def __str__(self):
        return (f"Position {self.symbol}: {self.quantity}@₹{self.entry_price} "
                f"(Current: ₹{self.current_price}, P&L: ₹{self.pnl:.2f})")

class OrderManager:
    """Manages all order operations and position tracking"""

    def __init__(self, config: TradingConfig, api: ZerodhaAPI):
        """Initialize order manager"""
        self.config = config
        self.api = api
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.pending_stop_losses: Dict[str, BuySignal] = {}  # Orders waiting for SL placement

        # Order tracking
        self.last_order_check = datetime.now()
        self.order_check_interval = 30  # seconds

        # Testing mode tracking
        self.is_testing_mode = config.trading_mode == 'TESTING'
        self.simulated_order_id_counter = 1000

        if self.is_testing_mode:
            logger.info("🧪 ORDER MANAGER: Running in TESTING mode - Orders will be simulated")
        else:
            logger.info("🚨 ORDER MANAGER: Running in LIVE mode - Real orders will be executed")

    def _generate_simulated_order_id(self) -> str:
        """Generate a simulated order ID for testing mode"""
        order_id = f"SIM_{self.simulated_order_id_counter}"
        self.simulated_order_id_counter += 1
        return order_id

    def _simulate_order_placement(self, symbol: str, transaction_type: str,
                                quantity: int, price: float, order_type: str,
                                product: str) -> str:
        """Simulate order placement for testing mode"""
        order_id = self._generate_simulated_order_id()

        logger.info("🧪 SIMULATED ORDER PLACEMENT:")
        logger.info(f"  Order ID: {order_id}")
        logger.info(f"  Symbol: {symbol}")
        logger.info(f"  Type: {transaction_type}")
        logger.info(f"  Quantity: {quantity}")
        logger.info(f"  Price: ₹{price}")
        logger.info(f"  Order Type: {order_type}")
        logger.info(f"  Product: {product}")
        logger.info("📝 NOTE: This is a SIMULATED order - No real trade executed")

        return order_id

    def _simulate_order_execution(self, order: Order, delay_seconds: int = 2):
        """Simulate order execution after a delay"""
        import threading
        import time

        def execute_after_delay():
            time.sleep(delay_seconds)
            order.status = OrderStatus.COMPLETE
            order.executed_price = order.price
            order.executed_quantity = order.quantity
            order.executed_time = datetime.now()

            logger.info(f"🧪 SIMULATED ORDER EXECUTED: {order}")

            # Handle post-execution logic
            self.handle_order_execution(order)

        # Start execution simulation in background
        thread = threading.Thread(target=execute_after_delay)
        thread.daemon = True
        thread.start()

    def place_buy_order(self, signal: BuySignal) -> Optional[str]:
        """Place a buy order based on buy signal"""
        try:
            if self.is_testing_mode:
                logger.info(f"🧪 TESTING MODE: Simulating buy order for {signal.symbol}")

                # Simulate order placement
                order_id = self._simulate_order_placement(
                    symbol=signal.symbol,
                    transaction_type="BUY",
                    quantity=signal.quantity,
                    price=signal.entry_price,
                    order_type=self.config.order_type,
                    product=self.config.product_type
                )
            else:
                logger.info(f"🚨 LIVE MODE: Placing real buy order for {signal.symbol}")

                # Place real order via API
                order_id = self.api.place_order(
                    symbol=signal.symbol,
                    transaction_type="BUY",
                    quantity=signal.quantity,
                    price=signal.entry_price,
                    order_type=self.config.order_type,
                    product=self.config.product_type
                )

            if not order_id:
                logger.error(f"❌ Failed to place buy order for {signal.symbol}")
                return None
            
            # Create order object
            order = Order(
                order_id=order_id,
                symbol=signal.symbol,
                order_type=OrderType.BUY,
                quantity=signal.quantity,
                price=signal.entry_price,
                status=OrderStatus.PENDING,
                timestamp=datetime.now()
            )
            
            # Store order
            self.orders[order_id] = order

            # Store signal for stop-loss placement after execution
            self.pending_stop_losses[order_id] = signal

            if self.is_testing_mode:
                logger.info(f"🧪 TESTING: Buy order simulated successfully: {order}")
                # Simulate order execution after a delay
                self._simulate_order_execution(order, delay_seconds=3)
            else:
                logger.info(f"✅ LIVE: Buy order placed successfully: {order}")

            return order_id
            
        except Exception as e:
            logger.error(f"❌ Error placing buy order for {signal.symbol}: {e}")
            return None
    
    def place_stop_loss_order(self, position: Position) -> Optional[str]:
        """Place stop-loss order for a position"""
        try:
            if self.is_testing_mode:
                logger.info(f"🧪 TESTING MODE: Simulating stop-loss order for {position.symbol}")

                # Simulate stop-loss order placement
                order_id = self._simulate_order_placement(
                    symbol=position.symbol,
                    transaction_type="SELL",
                    quantity=position.quantity,
                    price=position.stop_loss_price,
                    order_type="SL",
                    product=self.config.product_type
                )
            else:
                logger.info(f"🚨 LIVE MODE: Placing real stop-loss order for {position.symbol}")

                # Place real stop-loss order
                order_id = self.api.place_order(
                    symbol=position.symbol,
                    transaction_type="SELL",
                    quantity=position.quantity,
                    price=position.stop_loss_price,
                    order_type="SL",  # Stop-loss order
                    product=self.config.product_type
                )

            if not order_id:
                logger.error(f"❌ Failed to place stop-loss order for {position.symbol}")
                return None
            
            # Create stop-loss order object
            sl_order = Order(
                order_id=order_id,
                symbol=position.symbol,
                order_type=OrderType.STOP_LOSS,
                quantity=position.quantity,
                price=position.stop_loss_price,
                status=OrderStatus.PENDING,
                timestamp=datetime.now(),
                parent_order_id=position.buy_order.order_id
            )
            
            # Store order and update position
            self.orders[order_id] = sl_order
            position.stop_loss_order = sl_order

            if self.is_testing_mode:
                logger.info(f"🧪 TESTING: Stop-loss order simulated: {sl_order}")
            else:
                logger.info(f"✅ LIVE: Stop-loss order placed: {sl_order}")

            return order_id
            
        except Exception as e:
            logger.error(f"❌ Error placing stop-loss order for {position.symbol}: {e}")
            return None
    
    def update_order_status(self, order_id: str) -> bool:
        """Update order status from API"""
        try:
            if order_id not in self.orders:
                return False

            order = self.orders[order_id]

            # In testing mode, simulated orders are handled by _simulate_order_execution
            if self.is_testing_mode and order_id.startswith('SIM_'):
                # Simulated orders are updated by the simulation thread
                return True

            # Get order status from API for live orders
            api_order = self.api.get_order_status(order_id)
            if not api_order:
                return False
            

            
            # Update order status
            api_status = api_order.get('status', '').upper()
            if api_status == 'COMPLETE':
                order.status = OrderStatus.COMPLETE
                order.executed_price = float(api_order.get('average_price', order.price))
                order.executed_quantity = int(api_order.get('filled_quantity', 0))
                order.executed_time = datetime.now()
                
                logger.info(f"✅ Order executed: {order}")
                
                # Handle post-execution logic
                self.handle_order_execution(order)
                
            elif api_status == 'CANCELLED':
                order.status = OrderStatus.CANCELLED
                logger.info(f"❌ Order cancelled: {order}")
                
            elif api_status == 'REJECTED':
                order.status = OrderStatus.REJECTED
                logger.info(f"❌ Order rejected: {order}")
                
            elif api_status in ['OPEN', 'TRIGGER PENDING']:
                order.status = OrderStatus.OPEN
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error updating order status for {order_id}: {e}")
            return False
    
    def handle_order_execution(self, order: Order):
        """Handle post-execution logic for orders"""
        try:
            if order.order_type == OrderType.BUY and order.status == OrderStatus.COMPLETE:
                # Buy order executed - create position and place stop-loss
                self.create_position_from_buy_order(order)
                
            elif order.order_type == OrderType.STOP_LOSS and order.status == OrderStatus.COMPLETE:
                # Stop-loss executed - close position
                self.close_position_from_stop_loss(order)
                
        except Exception as e:
            logger.error(f"❌ Error handling order execution for {order.order_id}: {e}")
    
    def create_position_from_buy_order(self, buy_order: Order):
        """Create position after buy order execution"""
        try:
            # Get the original signal for stop-loss price
            signal = self.pending_stop_losses.get(buy_order.order_id)
            if not signal:
                logger.error(f"❌ No signal found for executed buy order {buy_order.order_id}")
                return
            
            # Create position
            position = Position(
                symbol=buy_order.symbol,
                quantity=buy_order.executed_quantity,
                entry_price=buy_order.executed_price,
                current_price=buy_order.executed_price,
                stop_loss_price=signal.stop_loss_price,
                buy_order=buy_order,
                entry_time=buy_order.executed_time
            )
            
            # Store position
            self.positions[buy_order.symbol] = position
            
            # Place stop-loss order
            self.place_stop_loss_order(position)
            
            # Remove from pending stop-losses
            del self.pending_stop_losses[buy_order.order_id]
            
            logger.info(f"✅ Position created: {position}")
            
        except Exception as e:
            logger.error(f"❌ Error creating position from buy order: {e}")
    
    def close_position_from_stop_loss(self, sl_order: Order):
        """Close position after stop-loss execution"""
        try:
            if sl_order.symbol in self.positions:
                position = self.positions[sl_order.symbol]
                
                # Update final P&L
                position.update_pnl(sl_order.executed_price)
                
                logger.info(f"🔴 Position closed via stop-loss: {position}")
                logger.info(f"Final P&L: ₹{position.pnl:.2f}")
                
                # Remove position
                del self.positions[sl_order.symbol]
                
        except Exception as e:
            logger.error(f"❌ Error closing position from stop-loss: {e}")
    
    def update_all_orders(self):
        """Update status of all pending/open orders"""
        try:
            pending_orders = [
                order_id for order_id, order in self.orders.items()
                if order.status in [OrderStatus.PENDING, OrderStatus.OPEN]
            ]
            
            for order_id in pending_orders:
                self.update_order_status(order_id)
                time.sleep(0.1)  # Small delay to avoid API rate limits
                
        except Exception as e:
            logger.error(f"❌ Error updating all orders: {e}")
    
    def update_position_prices(self):
        """Update current prices for all positions"""
        try:
            for symbol, position in self.positions.items():
                # Get current price
                quote = self.api.get_live_price(symbol)
                if quote and 'last_price' in quote:
                    current_price = float(quote['last_price'])
                    position.update_pnl(current_price)
                    
        except Exception as e:
            logger.error(f"❌ Error updating position prices: {e}")
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            if order_id not in self.orders:
                logger.error(f"❌ Order {order_id} not found")
                return False
            
            success = self.api.cancel_order(order_id)
            if success:
                self.orders[order_id].status = OrderStatus.CANCELLED
                logger.info(f"✅ Order {order_id} cancelled")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ Error cancelling order {order_id}: {e}")
            return False
    
    def get_open_orders(self) -> List[Order]:
        """Get all open orders"""
        return [
            order for order in self.orders.values()
            if order.status in [OrderStatus.PENDING, OrderStatus.OPEN]
        ]
    
    def get_positions(self) -> List[Position]:
        """Get all current positions"""
        return list(self.positions.values())
    
    def get_total_pnl(self) -> float:
        """Get total P&L across all positions"""
        return sum(position.pnl for position in self.positions.values())
    
    def periodic_update(self):
        """Perform periodic updates of orders and positions"""
        try:
            current_time = datetime.now()
            
            # Check if it's time to update
            if (current_time - self.last_order_check).seconds >= self.order_check_interval:
                logger.debug("🔄 Performing periodic update...")
                
                # Update order statuses
                self.update_all_orders()
                
                # Update position prices
                self.update_position_prices()
                
                self.last_order_check = current_time
                
        except Exception as e:
            logger.error(f"❌ Error in periodic update: {e}")
    
    def display_status(self):
        """Display current order and position status"""
        mode_icon = "🧪" if self.is_testing_mode else "🚨"
        print(f"\n📊 Order Manager Status ({mode_icon} {self.config.trading_mode} MODE)")
        print("="*60)
        
        # Display open orders
        open_orders = self.get_open_orders()
        print(f"Open Orders: {len(open_orders)}")
        for order in open_orders:
            print(f"  {order}")
        
        # Display positions
        positions = self.get_positions()
        print(f"\nActive Positions: {len(positions)}")
        total_pnl = 0
        for position in positions:
            print(f"  {position}")
            total_pnl += position.pnl
        
        print(f"\nTotal P&L: ₹{total_pnl:.2f}")

        if self.is_testing_mode:
            print("\n🧪 TESTING MODE ACTIVE - All orders are simulated")
        else:
            print("\n🚨 LIVE MODE ACTIVE - Real orders are being executed")

        print("="*60)


def test_order_manager():
    """Test function for order manager"""
    from config import TradingConfig

    # Load configuration from environment
    config = TradingConfig()

    # Initialize components
    api = ZerodhaAPI(config)
    order_manager = OrderManager(config, api)

    # Display status
    order_manager.display_status()


if __name__ == "__main__":
    test_order_manager()
