from kiteconnect import KiteConnect
import os
from dotenv import load_dotenv
from env_setup import update_access_token

# Load environment variables
load_dotenv()

# Get API credentials from environment variables
api_key = os.getenv('ZERODHA_API_KEY')
api_secret = os.getenv('ZERODHA_API_SECRET')

if not api_key or not api_secret:
    print("❌ API credentials not found in environment variables")
    print("Please set ZERODHA_API_KEY and ZERODHA_API_SECRET in your .env file")
    print("Run: python3 env_setup.py to configure environment variables")
    exit(1)

# Get request token from user input
print("🔑 Zerodha Access Token Generator")
print("="*40)
request_token = input("Enter the request_token from the login URL: ").strip()

if not request_token:
    print("❌ Request token is required")
    exit(1)

try:
    kite = KiteConnect(api_key=api_key)
    data = kite.generate_session(request_token, api_secret=api_secret)

    access_token = data["access_token"]
    kite.set_access_token(access_token)

    # Save access token to .env file
    update_access_token(access_token)

    print("\n✅ Access Token Generated Successfully!")
    print(f"Access Token: {access_token}")
    print("✅ Access token has been saved to .env file")
    print("\nYou can now run the trading algorithm!")

except Exception as e:
    print(f"❌ Error generating access token: {e}")
    print("Please check your request_token and try again")
