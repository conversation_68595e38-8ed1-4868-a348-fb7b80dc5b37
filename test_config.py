"""
Test script to verify environment configuration and API connectivity
"""

from config import TradingConfig
from env_setup import load_env_config, validate_env_config, display_env_config

def test_environment_config():
    """Test environment configuration loading"""
    print("🧪 Testing Environment Configuration")
    print("="*50)
    
    try:
        # Test environment loading
        env_config = load_env_config()
        print("✅ Environment variables loaded successfully")
        
        # Test TradingConfig with environment variables
        config = TradingConfig()
        print("✅ TradingConfig initialized successfully")
        
        # Display configuration
        display_env_config()
        
        # Validate configuration
        is_valid = validate_env_config()
        
        if is_valid:
            print("\n🎉 All tests passed! Configuration is ready.")
            return True
        else:
            print("\n❌ Configuration validation failed.")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def test_api_connection():
    """Test API connection (only if access token is available)"""
    print("\n🔌 Testing API Connection")
    print("="*30)
    
    try:
        config = TradingConfig()
        
        if not config.access_token:
            print("⚠️ Access token not set. Skipping API connection test.")
            print("Run: python3 generate_token.py to set access token")
            return False
        
        from zerodha_api import ZerodhaAPI
        
        api = ZerodhaAPI(config)
        
        if api.connected:
            print("✅ API connection successful!")
            
            # Test getting funds
            funds = api.get_funds()
            if funds:
                balance = funds.get('available', {}).get('live_balance', 'N/A')
                print(f"💰 Available balance: ₹{balance}")
            
            # Test getting live price for first symbol
            if config.buy_watchlist:
                symbol = config.buy_watchlist[0]
                price = api.get_live_price(symbol)
                if price:
                    print(f"📊 {symbol} price: ₹{price.get('last_price', 'N/A')}")
            
            return True
        else:
            print("❌ API connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing API connection: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Trading Algorithm Configuration Test")
    print("="*60)
    
    # Test environment configuration
    config_ok = test_environment_config()
    
    if config_ok:
        # Test API connection if possible
        api_ok = test_api_connection()
        
        if api_ok:
            print("\n🎉 All systems ready! You can now run the trading algorithm.")
            print("Run: python3 trading_algorithm.py")
        else:
            print("\n⚠️ API connection not tested or failed.")
            print("Make sure to generate access token before running the algorithm.")
    else:
        print("\n❌ Configuration issues found. Please fix them before proceeding.")
        print("Run: python3 env_setup.py to configure environment variables")

if __name__ == "__main__":
    main()
