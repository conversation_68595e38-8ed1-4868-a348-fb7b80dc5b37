# Quick Start Guide - Trading Algorithm

## 🚀 Complete Setup in 5 Steps

### Step 1: Install Dependencies
```bash
pip3 install -r requirements.txt
```

### Step 2: Configure Environment Variables
```bash
python3 env_setup.py
```
- Choose option 1: Setup Environment Variables
- Enter your Zerodha API credentials
- Configure risk settings (₹500 per trade recommended for beginners)
- Set up your watchlist (RELIANCE, TCS, HDFCBANK recommended)

### Step 3: Generate Access Token
```bash
# Get login URL
python3 zerodha_login.py
```
- Copy the URL and login to Zerodha
- Copy the request_token from the redirected URL

```bash
# Generate access token
python3 generate_token.py
```
- Paste the request_token when prompted
- Access token will be automatically saved to .env file

### Step 4: Test Configuration
```bash
python3 test_config.py
```
- Verify all settings are correct
- Test API connectivity

### Step 5: Choose Trading Mode
**IMPORTANT: Start in TESTING mode for safety!**

```bash
python3 env_setup.py
# Choose option 5: Switch Trading Mode
```

- **TESTING Mode**: Orders are simulated (safe for learning)
- **LIVE Mode**: Real orders with real money (use only when ready)

### Step 6: Run the Algorithm
```bash
python3 trading_algorithm.py
```

## 📋 Your Algorithm Settings

### ✅ Buy Logic (Exactly as requested):
1. **Skip first 3 candles** of the day
2. **Track lowest volume candle** after first 3 candles
3. **Bearish candle filter**: Only candles where close < open
4. **Direction filter**: Set to "Buy"
5. **Entry**: Buy at candle high
6. **Stop-loss**: Automatically placed at candle low
7. **Position sizing**: Quantity = Risk Amount / Candle Length

### 🛡️ Risk Management:
- **Per trade loss limit**: ₹500 (configurable)
- **Max trades per day**: 3 (configurable)
- **Automatic stop-loss**: Placed immediately after buy order execution

### 📊 Default Settings:
- **Timeframe**: 5-minute candles
- **Trading hours**: 09:15 - 15:30
- **Watchlist**: RELIANCE, TCS, HDFCBANK
- **Order type**: LIMIT orders
- **Product type**: MIS (intraday)

## 🔧 Environment Variables (.env file)

Your sensitive data is stored securely in the `.env` file:

```bash
# Zerodha API Credentials
ZERODHA_API_KEY=your_api_key
ZERODHA_API_SECRET=your_api_secret
ZERODHA_ACCESS_TOKEN=your_access_token

# Trading Configuration
PER_TRADE_LOSS_LIMIT=500.0
MAX_TRADES_PER_DAY=3
TIMEFRAME=5minute

# Watchlist
BUY_WATCHLIST=RELIANCE,TCS,HDFCBANK
SELL_WATCHLIST=RELIANCE,TCS,HDFCBANK

# Trading Mode (TESTING or LIVE)
TRADING_MODE=TESTING
```

## 🎯 Key Commands

### Configuration Management:
```bash
python3 env_setup.py          # Manage environment variables
python3 test_config.py        # Test configuration
```

### Trading Mode Control:
```bash
python3 env_setup.py          # Option 5: Switch between TESTING/LIVE
```

### Token Management:
```bash
python3 zerodha_login.py      # Get login URL
python3 generate_token.py     # Generate access token
```

### Running the Algorithm:
```bash
python3 trading_algorithm.py  # Start trading
```

## ⚠️ Important Notes for Beginners

### Before Going Live:
1. **Start in TESTING mode**: Always begin with simulated orders
2. **Understand the algorithm**: Learn how the buy logic works
3. **Monitor testing results**: Watch simulated trades and signals
4. **Only switch to LIVE when confident**: Use real money only after thorough testing

### Safety Features:
- ✅ **TESTING Mode**: Simulate orders without real money
- ✅ Automatic stop-loss placement
- ✅ Daily trade limits
- ✅ Risk per trade limits
- ✅ Market hours validation
- ✅ Fund availability checks

### Files to Keep Secure:
- `.env` file (contains your API credentials)
- Never commit `.env` to version control
- Use `.env.example` as a template for others

## 🆘 Troubleshooting

### Common Issues:

**"API credentials not found"**
```bash
python3 env_setup.py  # Set up credentials
```

**"Access token expired"**
```bash
python3 generate_token.py  # Generate new token
```

**"No buy signals found"**
- Check if market is open
- Verify watchlist symbols are correct
- Ensure first 3 candles have passed

**"Insufficient funds"**
- Check your account balance
- Reduce per trade loss limit

### Getting Help:
1. Check the logs: `trading_algorithm.log`
2. Run test script: `python3 test_config.py`
3. Validate config: `python3 env_setup.py` → option 5

## 🎉 You're Ready!

Once you complete these steps, your trading algorithm will:
- Monitor your watchlist during market hours
- Identify lowest volume bearish candles
- Place buy orders with automatic stop-losses
- Manage risk according to your settings
- Log all activities for review

**Happy Trading! 📈**
