"""
Environment Configuration Setup
Helps manage environment variables for the trading algorithm
"""

import os
from dotenv import load_dotenv, set_key, find_dotenv

def load_env_config():
    """Load environment variables from .env file"""
    load_dotenv()
    return {
        'api_key': os.getenv('ZERODHA_API_KEY', ''),
        'api_secret': os.getenv('ZERODHA_API_SECRET', ''),
        'access_token': os.getenv('ZERODHA_ACCESS_TOKEN', ''),
        'per_trade_loss_limit': float(os.getenv('PER_TRADE_LOSS_LIMIT', 1000.0)),
        'max_trades_per_day': int(os.getenv('MAX_TRADES_PER_DAY', 5)),
        'timeframe': os.getenv('TIMEFRAME', '1minute'),
        'trading_start_time': os.getenv('TRADING_START_TIME', '09:15'),
        'trading_end_time': os.getenv('TRADING_END_TIME', '15:30'),
        'buy_watchlist': [s.strip() for s in os.getenv('BUY_WATCHLIST', '').split(',') if s.strip()],
        'sell_watchlist': [s.strip() for s in os.getenv('SELL_WATCHLIST', '').split(',') if s.strip()],
        'order_type': os.getenv('ORDER_TYPE', 'LIMIT'),
        'product_type': os.getenv('PRODUCT_TYPE', 'MIS'),
        'skip_first_candles': int(os.getenv('SKIP_FIRST_CANDLES', 3)),
        'direction_filter': os.getenv('DIRECTION_FILTER', 'Buy'),
        'trading_mode': os.getenv('TRADING_MODE', 'TESTING').upper()
    }

def update_env_variable(key: str, value: str):
    """Update a single environment variable in .env file"""
    env_file = find_dotenv()
    if not env_file:
        env_file = '.env'
    
    set_key(env_file, key, value)
    print(f"✅ Updated {key} in .env file")

def update_access_token(access_token: str):
    """Update access token in .env file"""
    update_env_variable('ZERODHA_ACCESS_TOKEN', access_token)

def update_watchlist(watchlist_type: str, symbols: list):
    """Update watchlist in .env file"""
    symbols_str = ','.join(symbols)
    if watchlist_type.lower() == 'buy':
        update_env_variable('BUY_WATCHLIST', symbols_str)
    elif watchlist_type.lower() == 'sell':
        update_env_variable('SELL_WATCHLIST', symbols_str)
    else:
        raise ValueError("watchlist_type must be 'buy' or 'sell'")

def display_env_config():
    """Display current environment configuration"""
    config = load_env_config()
    
    print("\n" + "="*50)
    print("ENVIRONMENT CONFIGURATION")
    print("="*50)
    print(f"API Key: {config['api_key'][:10]}..." if config['api_key'] else "API Key: Not set")
    print(f"API Secret: {config['api_secret'][:10]}..." if config['api_secret'] else "API Secret: Not set")
    print(f"Access Token: {config['access_token'][:20]}..." if config['access_token'] else "Access Token: Not set")
    print(f"Per Trade Loss Limit: ₹{config['per_trade_loss_limit']}")
    print(f"Max Trades Per Day: {config['max_trades_per_day']}")
    print(f"Timeframe: {config['timeframe']}")
    print(f"Trading Hours: {config['trading_start_time']} - {config['trading_end_time']}")
    print(f"Skip First Candles: {config['skip_first_candles']}")
    print(f"Direction Filter: {config['direction_filter']}")
    print(f"Order Type: {config['order_type']}")
    print(f"Product Type: {config['product_type']}")
    print(f"Trading Mode: {config['trading_mode']}")
    print(f"\nBuy Watchlist ({len(config['buy_watchlist'])} symbols):")
    for symbol in config['buy_watchlist']:
        print(f"  - {symbol}")
    print(f"\nSell Watchlist ({len(config['sell_watchlist'])} symbols):")
    for symbol in config['sell_watchlist']:
        print(f"  - {symbol}")
    print("="*50)

def setup_env_interactive():
    """Interactive setup for environment variables"""
    print("🔧 Environment Variables Setup")
    print("="*40)
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("Creating .env file...")
        with open('.env', 'w') as f:
            f.write("# Zerodha Trading Algorithm Environment Variables\n")
    
    config = load_env_config()
    
    print("\n🔑 Zerodha API Credentials")
    print("-" * 30)
    
    # API Key
    current_key = config['api_key'][:10] + "..." if config['api_key'] else "Not set"
    new_key = input(f"API Key [{current_key}]: ").strip()
    if new_key:
        update_env_variable('ZERODHA_API_KEY', new_key)
    
    # API Secret
    current_secret = config['api_secret'][:10] + "..." if config['api_secret'] else "Not set"
    new_secret = input(f"API Secret [{current_secret}]: ").strip()
    if new_secret:
        update_env_variable('ZERODHA_API_SECRET', new_secret)
    
    # Access Token
    current_token = config['access_token'][:20] + "..." if config['access_token'] else "Not set"
    new_token = input(f"Access Token [{current_token}]: ").strip()
    if new_token:
        update_env_variable('ZERODHA_ACCESS_TOKEN', new_token)
    
    print("\n📊 Risk Management")
    print("-" * 30)
    
    # Per trade loss limit
    new_loss_limit = input(f"Per trade loss limit (₹) [{config['per_trade_loss_limit']}]: ").strip()
    if new_loss_limit:
        try:
            update_env_variable('PER_TRADE_LOSS_LIMIT', new_loss_limit)
        except ValueError:
            print("❌ Invalid number format")
    
    # Max trades per day
    new_max_trades = input(f"Max trades per day [{config['max_trades_per_day']}]: ").strip()
    if new_max_trades:
        try:
            update_env_variable('MAX_TRADES_PER_DAY', new_max_trades)
        except ValueError:
            print("❌ Invalid number format")
    
    print("\n📈 Trading Settings")
    print("-" * 30)
    
    # Timeframe
    timeframes = ["1minute", "3minute", "5minute", "15minute", "30minute", "60minute"]
    print("Available timeframes:", ", ".join(timeframes))
    new_timeframe = input(f"Timeframe [{config['timeframe']}]: ").strip()
    if new_timeframe and new_timeframe in timeframes:
        update_env_variable('TIMEFRAME', new_timeframe)

    # Trading mode
    print(f"\nTrading Mode Options:")
    print("  TESTING - Simulate orders (no real trades)")
    print("  LIVE - Execute real orders in market")
    new_mode = input(f"Trading Mode [{config['trading_mode']}]: ").strip().upper()
    if new_mode and new_mode in ['TESTING', 'LIVE']:
        update_env_variable('TRADING_MODE', new_mode)
        if new_mode == 'LIVE':
            print("⚠️ WARNING: LIVE mode will execute real trades with real money!")
            confirm = input("Are you sure you want to enable LIVE trading? (yes/no): ").strip().lower()
            if confirm != 'yes':
                update_env_variable('TRADING_MODE', 'TESTING')
                print("✅ Trading mode set to TESTING for safety")

    print("\n📋 Watchlist")
    print("-" * 30)
    
    # Buy watchlist
    current_buy = ','.join(config['buy_watchlist']) if config['buy_watchlist'] else "Not set"
    print(f"Current buy watchlist: {current_buy}")
    new_buy_watchlist = input("New buy watchlist (comma-separated, or press Enter to keep current): ").strip()
    if new_buy_watchlist:
        update_env_variable('BUY_WATCHLIST', new_buy_watchlist.upper())
    
    # Sell watchlist
    current_sell = ','.join(config['sell_watchlist']) if config['sell_watchlist'] else "Not set"
    print(f"Current sell watchlist: {current_sell}")
    use_same = input("Use same as buy watchlist? (y/n) [y]: ").strip().lower()
    if use_same != 'n':
        if new_buy_watchlist:
            update_env_variable('SELL_WATCHLIST', new_buy_watchlist.upper())
        elif config['buy_watchlist']:
            update_env_variable('SELL_WATCHLIST', ','.join(config['buy_watchlist']))
    else:
        new_sell_watchlist = input("New sell watchlist (comma-separated): ").strip()
        if new_sell_watchlist:
            update_env_variable('SELL_WATCHLIST', new_sell_watchlist.upper())
    
    print("\n✅ Environment variables updated!")
    
    # Reload and display
    load_dotenv(override=True)
    display_env_config()

def validate_env_config():
    """Validate environment configuration"""
    config = load_env_config()
    errors = []
    
    # Check required fields
    if not config['api_key']:
        errors.append("ZERODHA_API_KEY is not set")
    
    if not config['api_secret']:
        errors.append("ZERODHA_API_SECRET is not set")
    
    if not config['buy_watchlist']:
        errors.append("BUY_WATCHLIST is empty")
    
    # Check numeric values
    if config['per_trade_loss_limit'] <= 0:
        errors.append("PER_TRADE_LOSS_LIMIT must be positive")
    
    if config['max_trades_per_day'] <= 0:
        errors.append("MAX_TRADES_PER_DAY must be positive")
    
    # Check timeframe
    valid_timeframes = ["1minute", "3minute", "5minute", "15minute", "30minute", "60minute"]
    if config['timeframe'] not in valid_timeframes:
        errors.append(f"TIMEFRAME must be one of {valid_timeframes}")

    # Check trading mode
    valid_modes = ["TESTING", "LIVE"]
    if config['trading_mode'] not in valid_modes:
        errors.append(f"TRADING_MODE must be one of {valid_modes}")

    if errors:
        print("❌ Environment configuration errors:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("✅ Environment configuration is valid")
    return True

def main():
    """Main function for environment setup"""
    while True:
        print("\n🔧 Environment Configuration Menu")
        print("="*40)
        print("1. Setup Environment Variables")
        print("2. Display Current Configuration")
        print("3. Update Access Token")
        print("4. Update Watchlist")
        print("5. Switch Trading Mode (TESTING/LIVE)")
        print("6. Validate Configuration")
        print("7. Exit")
        
        choice = input("\nSelect option (1-7): ").strip()
        
        if choice == "1":
            setup_env_interactive()
        elif choice == "2":
            display_env_config()
        elif choice == "3":
            token = input("Enter new access token: ").strip()
            if token:
                update_access_token(token)
        elif choice == "4":
            watchlist_type = input("Watchlist type (buy/sell): ").strip().lower()
            if watchlist_type in ['buy', 'sell']:
                symbols_str = input("Enter symbols (comma-separated): ").strip()
                if symbols_str:
                    symbols = [s.strip().upper() for s in symbols_str.split(',')]
                    update_watchlist(watchlist_type, symbols)
        elif choice == "5":
            # Switch trading mode
            config = load_env_config()
            current_mode = config['trading_mode']
            print(f"\n🔄 Current Trading Mode: {current_mode}")
            print("Available modes:")
            print("  TESTING - Simulate orders (no real trades)")
            print("  LIVE - Execute real orders in market")

            if current_mode == 'TESTING':
                switch_to = input("Switch to LIVE mode? (yes/no): ").strip().lower()
                if switch_to == 'yes':
                    print("⚠️ WARNING: LIVE mode will execute real trades with real money!")
                    confirm = input("Are you absolutely sure? (yes/no): ").strip().lower()
                    if confirm == 'yes':
                        update_env_variable('TRADING_MODE', 'LIVE')
                        print("✅ Trading mode switched to LIVE")
                        print("🚨 CAUTION: Real orders will now be executed!")
                    else:
                        print("✅ Staying in TESTING mode")
            else:
                switch_to = input("Switch to TESTING mode? (yes/no): ").strip().lower()
                if switch_to == 'yes':
                    update_env_variable('TRADING_MODE', 'TESTING')
                    print("✅ Trading mode switched to TESTING")
                    print("📝 Orders will now be simulated only")
        elif choice == "6":
            validate_env_config()
        elif choice == "7":
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid option. Please try again.")

if __name__ == "__main__":
    main()
